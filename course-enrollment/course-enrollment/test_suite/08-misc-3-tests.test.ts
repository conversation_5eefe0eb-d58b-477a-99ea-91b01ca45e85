import {
  apiClient,
  createHeaders,
  TEST_USERS,
  createTermEnrollmentOpen,
  createTermEnrollmentClosed, createCourseOpen,
  createCourseWithWaitlist,
  createStudentEnrolledCourse,
  validateErrorEnvelope,
  getSuccessData, ApiErrorId,
  UserRole,
  EnrollmentState,
  CourseState,
  DeliveryMode,
  calculateCourseCost, createTermPlanning,
  CreateCoursePayload
} from './helpers';
import { v4 as uuidv4 } from 'uuid';

describe('TC-159: Payment when no balance exists', () => {
  it('should initialize ledger at zero and reject overpayment for student with no enrollments', async () => {
    const { term } = await createTermEnrollmentOpen();
    const uniqueStudentId = uuidv4();
    const studentHeaders = createHeaders(uniqueStudentId, UserRole.STUDENT);

    const response = await apiClient.makePayment(
      term.id,
      uniqueStudentId,
      { amount: 1000 },
      studentHeaders
    );

    validateErrorEnvelope(response, {
      expectedErrorId: ApiErrorId.ERR_OVERPAY_NOT_ALLOWED
    });
  });
});

describe('TC-160: Registrar overpayment', () => {
  it('should reject registrar overpayment attempt with ERR_OVERPAY_NOT_ALLOWED', async () => {
    const { term, course, enrollment } = await createStudentEnrolledCourse(TEST_USERS.STUDENT_A.id);
    const owedAmount = calculateCourseCost(course.credits);
    const overpaymentAmount = owedAmount + 1000;

    const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
    const response = await apiClient.makePayment(
      term.id,
      TEST_USERS.STUDENT_A.id,
      { amount: overpaymentAmount },
      registrarHeaders
    );

    validateErrorEnvelope(response, {
      expectedErrorId: ApiErrorId.ERR_OVERPAY_NOT_ALLOWED
    });
  });
});

describe('TC-161: Negative payment amounts', () => {
  it('should reject payment with negative amount', async () => {
    const { term, course, enrollment } = await createStudentEnrolledCourse(TEST_USERS.STUDENT_A.id);

    const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);
    const response = await apiClient.makePayment(
      term.id,
      TEST_USERS.STUDENT_A.id,
      { amount: -1000 },
      studentHeaders
    );

    validateErrorEnvelope(response, {
      expectedErrorId: ApiErrorId.ERR_INVALID_PAYMENT_AMOUNT
    });
  });
});

describe('TC-162: Post-failure state verification after failed drop', () => {
  it('should maintain enrollment state after failed drop attempt due to drop limit', async () => {
    const { term } = await createTermEnrollmentOpen();
    const uniqueStudentId = uuidv4();
    const studentHeaders = createHeaders(uniqueStudentId, UserRole.STUDENT);
    const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);

    const courses = [];
    const uniqueId = Math.floor(Math.random() * 10000);
    const professorHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);

    for (let i = 0; i < 4; i++) {
      const courseCode = `CS${(100 + uniqueId + i).toString().slice(-3)}`;

      const createResp = await apiClient.createCourse(
        term.id,
        {
          code: courseCode,
          title: `Test Course ${i + 1}`,
          credits: 3,
          capacity: 10,
          delivery_mode: DeliveryMode.IN_PERSON,
          location: `Room ${100 + i}`
        },
        professorHeaders
      );
      const draftCourse = getSuccessData(createResp);

      const publishResp = await apiClient.publishCourse(
        term.id,
        draftCourse.id,
        { revision: draftCourse.revision },
        professorHeaders
      );
      const course = getSuccessData(publishResp);
      courses.push(course);

      await apiClient.createEnrollment(term.id, course.id, {}, studentHeaders);
    }

    for (let i = 0; i < 3; i++) {
      const enrollmentsResp = await apiClient.listEnrollments(term.id, courses[i].id, {}, registrarHeaders);
      const enrollments = getSuccessData(enrollmentsResp);
      const enrollment = enrollments.find(e => e.student_id === uniqueStudentId)!;

      await apiClient.dropEnrollment(
        term.id,
        courses[i].id,
        enrollment.id,
        { revision: enrollment.revision },
        studentHeaders
      );
    }

    const enrollmentsResp = await apiClient.listEnrollments(term.id, courses[3].id, {}, registrarHeaders);
    const enrollments = getSuccessData(enrollmentsResp);
    const fourthEnrollment = enrollments.find(e => e.student_id === uniqueStudentId)!;
    const originalState = fourthEnrollment.state;
    const originalRevision = fourthEnrollment.revision;

    const dropResponse = await apiClient.dropEnrollment(
      term.id,
      courses[3].id,
      fourthEnrollment.id,
      { revision: fourthEnrollment.revision },
      studentHeaders
    );

    validateErrorEnvelope(dropResponse, {
      expectedErrorId: ApiErrorId.ERR_TOO_MANY_DROPS
    });

    const postFailureResp = await apiClient.getEnrollment(
      term.id,
      courses[3].id,
      fourthEnrollment.id,
      registrarHeaders
    );
    const postFailureEnrollment = getSuccessData(postFailureResp);

    expect(postFailureEnrollment.state).toBe(originalState);
    expect(postFailureEnrollment.revision).toBe(originalRevision);
  });
});

describe('TC-163: Ledger updates during waitlist promotion', () => {
  it('should correctly update ledger when waitlisted student is promoted to enrolled', async () => {
    const { term, course, enrollments } = await createCourseWithWaitlist();
    const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);

    expect(enrollments.studentC.state).toBe(EnrollmentState.WAITLISTED);

    const dropResponse = await apiClient.dropEnrollment(
      term.id,
      course.id,
      enrollments.studentA.id,
      { revision: enrollments.studentA.revision },
      createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT)
    );
    const droppedEnrollment = getSuccessData(dropResponse);
    expect(droppedEnrollment.state).toBe(EnrollmentState.DROPPED);

    const promotedResp = await apiClient.getEnrollment(
      term.id,
      course.id,
      enrollments.studentC.id,
      registrarHeaders
    );
    const promotedEnrollment = getSuccessData(promotedResp);
    expect(promotedEnrollment.state).toBe(EnrollmentState.ENROLLED);

    const expectedCost = calculateCourseCost(course.credits);

    const paymentResponse = await apiClient.makePayment(
      term.id,
      TEST_USERS.STUDENT_C.id,
      { amount: expectedCost },
      createHeaders(TEST_USERS.STUDENT_C.id, UserRole.STUDENT)
    );
    const paymentResult = getSuccessData(paymentResponse);
    expect(paymentResult.new_balance).toBe(0);
  });
});

describe('TC-164: Refunds on course cancellation', () => {
  it('should handle course cancellation and ledger updates appropriately', async () => {
    const { term, course, enrollments } = await createCourseWithWaitlist();
    const professorHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);
    const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);

    const partialPayment = 15000;
    await apiClient.makePayment(
      term.id,
      TEST_USERS.STUDENT_A.id,
      { amount: partialPayment },
      createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT)
    );

    await apiClient.makePayment(
      term.id,
      TEST_USERS.STUDENT_B.id,
      { amount: partialPayment },
      createHeaders(TEST_USERS.STUDENT_B.id, UserRole.STUDENT)
    );

    const courseResp = await apiClient.getCourse(term.id, course.id, registrarHeaders);
    const updatedCourse = getSuccessData(courseResp);

    const cancelResponse = await apiClient.cancelCourse(
      term.id,
      course.id,
      { revision: updatedCourse.revision },
      professorHeaders
    );
    const cancelledCourse = getSuccessData(cancelResponse);
    expect(cancelledCourse.state).toBe(CourseState.CANCELLED);

    const enrollmentsResp = await apiClient.listEnrollments(term.id, course.id, {}, registrarHeaders);
    const updatedEnrollments = getSuccessData(enrollmentsResp);

    updatedEnrollments.forEach(enrollment => {
      expect(enrollment.state).toBe(EnrollmentState.DROPPED);
    });

    const paymentRespC = await apiClient.makePayment(
      term.id,
      TEST_USERS.STUDENT_C.id,
      { amount: 1 },
      createHeaders(TEST_USERS.STUDENT_C.id, UserRole.STUDENT)
    );
    validateErrorEnvelope(paymentRespC, {
      expectedErrorId: ApiErrorId.ERR_OVERPAY_NOT_ALLOWED
    });
  });
});

describe('TC-165: Dropping a waitlisted enrollment', () => {
  it('should allow dropping waitlisted enrollment without affecting ledger or seat counts', async () => {
    const { term, course, enrollments } = await createCourseWithWaitlist();
    const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);

    expect(enrollments.studentC.state).toBe(EnrollmentState.WAITLISTED);

    const preDropPayment = await apiClient.makePayment(
      term.id,
      TEST_USERS.STUDENT_C.id,
      { amount: 1 },
      createHeaders(TEST_USERS.STUDENT_C.id, UserRole.STUDENT)
    );
    validateErrorEnvelope(preDropPayment, {
      expectedErrorId: ApiErrorId.ERR_OVERPAY_NOT_ALLOWED
    });

    const dropResponse = await apiClient.dropEnrollment(
      term.id,
      course.id,
      enrollments.studentC.id,
      { revision: enrollments.studentC.revision },
      createHeaders(TEST_USERS.STUDENT_C.id, UserRole.STUDENT)
    );
    const droppedEnrollment = getSuccessData(dropResponse);
    expect(droppedEnrollment.state).toBe(EnrollmentState.DROPPED);

    const courseResp = await apiClient.getCourse(term.id, course.id, registrarHeaders);
    const updatedCourse = getSuccessData(courseResp);
    expect(updatedCourse.enrolled_count).toBe(2);

    const postDropPayment = await apiClient.makePayment(
      term.id,
      TEST_USERS.STUDENT_C.id,
      { amount: 1 },
      createHeaders(TEST_USERS.STUDENT_C.id, UserRole.STUDENT)
    );
    validateErrorEnvelope(postDropPayment, {
      expectedErrorId: ApiErrorId.ERR_OVERPAY_NOT_ALLOWED
    });
  });
});

describe('TC-166: Registrar enrollment into concluded term', () => {
  it('should reject registrar enrollment attempt into concluded term', async () => {
    const { term, course } = await createCourseOpen('CS999', 10, TEST_USERS.PROFESSOR_A.id);
    const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);

    const closedResp = await apiClient.closeTermRegistration(
      term.id,
      { revision: term.revision },
      registrarHeaders
    );
    const closedTerm = getSuccessData(closedResp);

    const concludedResp = await apiClient.concludeTerm(
      closedTerm.id,
      { revision: closedTerm.revision },
      registrarHeaders
    );
    const concludedTerm = getSuccessData(concludedResp);

    const enrollResponse = await apiClient.createEnrollment(
      concludedTerm.id,
      course.id,
      { student_id: TEST_USERS.STUDENT_A.id },
      registrarHeaders
    );

    validateErrorEnvelope(enrollResponse, {
      expectedErrorId: ApiErrorId.ERR_TERM_NOT_ACTIVE
    });
  });
});

describe('TC-167: Stale revision on close-registration', () => {
  it('should reject close-registration with stale revision', async () => {
    const { term } = await createTermEnrollmentOpen();
    const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);

    const staleRevision = term.revision - 1;

    const response = await apiClient.closeTermRegistration(
      term.id,
      { revision: staleRevision },
      registrarHeaders
    );

    validateErrorEnvelope(response, {
      expectedErrorId: ApiErrorId.ERR_REV_CONFLICT
    });

    const termResp = await apiClient.getTerm(term.id, registrarHeaders);
    const unchangedTerm = getSuccessData(termResp);
    expect(unchangedTerm.state).toBe('ENROLLMENT_OPEN');
    expect(unchangedTerm.revision).toBe(term.revision);
  });
});


describe('TC-168: Create Term with Long Name', () => {
  // SKIPPED: This test asserts a behavior not specified in the PRD. The PRD only requires term names to be non-empty and unique, with no length limit specified.
  it.skip('should reject term creation with name exceeding 100 characters', async () => {
    const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);

    const longName = 'A'.repeat(150);

    const response = await apiClient.createTerm(
      { name: longName },
      registrarHeaders
    );

    const error = response.data as any;
    expect(error.data.error_id).toBe(ApiErrorId.ERR_INVALID_FIELD_LENGTH);
  });
});

describe('TC-169: Conclude Term with Draft Course Present', () => {
  // SKIPPED: This test assumes concluding a term automatically completes DRAFT courses, but the PRD only specifies that IN_PROGRESS courses are transitioned to COMPLETED. DRAFT courses are not mentioned in the term conclusion behavior.
  it.skip('should automatically complete draft courses when concluding term', async () => {
    const { term } = await createTermEnrollmentClosed();
    const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
    const professorHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);

    const courseData: CreateCoursePayload = {
      code: 'DRFT101',
      title: 'Draft Course Test',
      credits: 3,
      capacity: 10,
      delivery_mode: DeliveryMode.IN_PERSON,
      location: 'Room 101'
    };
    const courseResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
    const draftCourse = getSuccessData(courseResp);
    expect(draftCourse.state).toBe(CourseState.DRAFT);

    const concludeResp = await apiClient.concludeTerm(
      term.id,
      { revision: term.revision },
      registrarHeaders
    );
    expect(concludeResp.status).toBe(200);

    const updatedCourseResp = await apiClient.getCourse(term.id, draftCourse.id, professorHeaders);
    const updatedCourse = getSuccessData(updatedCourseResp);
    expect(updatedCourse.state).toBe(CourseState.COMPLETED);
  });
});

describe('TC-170: Course Creation without delivery_mode', () => {
  it('should reject course creation without delivery_mode field', async () => {
    const { term } = await createTermPlanning();
    const professorHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);

    const courseData = {
      code: 'NMD101',
      title: 'Course Without Delivery Mode',
      credits: 3,
      capacity: 10
      // delivery_mode is intentionally omitted
    };

    const response = await apiClient.createCourse(term.id, courseData as any, professorHeaders);

    // PRD explicitly requires delivery_mode field for course creation
    validateErrorEnvelope(response, {
      expectedErrorId: ApiErrorId.ERR_MISSING_REQUIRED_FIELD
    });
  });
});

describe('TC-171: Publish Course without revision', () => {
  it('should reject publishing course without revision field', async () => {
    const { term } = await createTermEnrollmentOpen();
    const professorHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);

    const courseData: CreateCoursePayload = {
      code: 'PUB101',
      title: 'Publish Test Course',
      credits: 3,
      capacity: 10,
      delivery_mode: DeliveryMode.IN_PERSON,
      location: 'Room 101'
    };
    const courseResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
    const course = getSuccessData(courseResp);

    const publishResp = await apiClient.publishCourse(
      term.id,
      course.id,
      {} as any, // revision field is intentionally omitted
      professorHeaders
    );

    // PRD requires revision for optimistic locking during publish operations
    validateErrorEnvelope(publishResp, {
      expectedErrorId: ApiErrorId.ERR_MISSING_REQUIRED_FIELD
    });
  });
});

describe('TC-172: Cancel Course without revision', () => {
  it('should reject cancelling course without revision field', async () => {
    const { term } = await createTermEnrollmentOpen();
    const professorHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);

    const courseData: CreateCoursePayload = {
      code: 'CAN101',
      title: 'Cancel Test Course',
      credits: 3,
      capacity: 10,
      delivery_mode: DeliveryMode.IN_PERSON,
      location: 'Room 101'
    };
    const courseResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
    const course = getSuccessData(courseResp);

    await apiClient.publishCourse(
      term.id,
      course.id,
      { revision: course.revision },
      professorHeaders
    );

    const cancelResp = await apiClient.cancelCourse(
      term.id,
      course.id,
      {} as any, // revision field is intentionally omitted
      professorHeaders
    );

    // PRD requires revision for optimistic locking during cancel operations
    validateErrorEnvelope(cancelResp, {
      expectedErrorId: ApiErrorId.ERR_MISSING_REQUIRED_FIELD
    });
  });
});

describe('TC-173: Course Description Length Validation', () => {
  it('should reject course creation with description exceeding 1000 characters', async () => {
    const { term } = await createTermPlanning();
    const professorHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);

    const longDescription = 'A'.repeat(1001);

    const courseData: CreateCoursePayload = {
      code: 'LNG101',
      title: 'Long Description Test',
      description: longDescription,
      credits: 3,
      capacity: 10,
      delivery_mode: DeliveryMode.IN_PERSON,
      location: 'Room 101'
    };

    const response = await apiClient.createCourse(term.id, courseData, professorHeaders);

    const error = response.data as any;
    expect(error.data.error_id).toBe(ApiErrorId.ERR_INVALID_FIELD_LENGTH);
  });
});
